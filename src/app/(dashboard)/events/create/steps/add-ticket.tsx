'use client';
import { useMemo, useState } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { CreateEventLayout } from '@/components/layouts';
import {
  Button,
  ControlledInput,
  CostSelector,
  DateTimePicker,
  FeatureToggle,
  Input,
  Modal,
  P,
} from '@/components/ui';
import {
  type CreateEventFormType,
  useCreateEventForm,
  useFieldBlurAndFilled,
} from '@/lib';
import { formatDate, formatDateTime } from '@/lib';
import { Counter } from '@/components/ui/counter';
import { type StepProps } from '../page';

export default function AddTicket({ setStep, fromStep }: StepProps) {
  const { watch, control, setValue, getValues } =
    useFormContext<CreateEventFormType>();
  const { defaultTicket } = useCreateEventForm();
  const { fieldStates } = useFieldBlurAndFilled<CreateEventFormType>([
    'tickets.0.name',
    'tickets.0.price',
    'tickets.0.quantity',
    'tickets.0.presalePrice',
    'tickets.0.presaleQuantity',
    'tickets.0.presaleStartDatetime',
    'tickets.0.presaleEndDatetime',
    'tickets.0.purchaseLimit',
    'tickets.0.description',
    'tickets.0.startDatetime',
    'tickets.0.endDatetime',
  ]);
  const { append, update } = useFieldArray({
    control,
    name: 'tickets',
  });

  // can enable all features
  const name = watch(`tickets.0.name`);
  const price = watch(`tickets.0.price`);
  const quantity = watch(`tickets.0.quantity`);
  const canEnable = name && price && quantity;

  // presale toggle and modal
  function enablePresale() {
    setValue(`tickets.0.hasPresale`, true);
    setValue(`tickets.0.presalePrice`, '');
    setValue(`tickets.0.presaleQuantity`, '' as unknown as number);
  }

  function disablePresale() {
    if (watch(`tickets.0.hasPresale`)) {
      setValue(`tickets.0.hasPresale`, false, { shouldValidate: true });
    }
  }
  const [presaleModal, setPresaleModal] = useState(false);

  const togglePresale = (checked: boolean) => {
    if (checked) {
      enablePresale();
      setPresaleModal(true);
    } else {
      disablePresale();
      setPresaleModal(false);
    }
  };
  const handleAddPresale = () => {
    setPresaleModal(false);
  };

  const handlePresaleModalDismiss = () => {
    if (
      !watch('tickets.0.presalePrice') ||
      !watch('tickets.0.presaleStartDatetime') ||
      !watch('tickets.0.presaleEndDatetime') ||
      !watch('tickets.0.presaleQuantity')
    ) {
      disablePresale();
      setPresaleModal(false);
    }
  };

  const eventEnd = watch('endDatetime');

  const presaleStartDatetime = watch(`tickets.0.presaleStartDatetime`);

  const presaleStartDatetimeLabel = presaleStartDatetime
    ? formatDateTime(presaleStartDatetime)
    : 'Start date and time';

  const presaleEndDatetime = watch(`tickets.0.presaleEndDatetime`);

  const presaleEndDatetimeLabel = presaleEndDatetime
    ? formatDateTime(presaleEndDatetime)
    : 'End date and time';

  const handlePresaleStartDateConfirm = (date: Date) => {
    if (date < new Date()) {
      alert('Presale start date cannot be in the past.');
      return;
    }
    if (date >= eventEnd) {
      alert('Presale start date must before the end of event.');
      return;
    }
    const ticketStart = watch(`tickets.0.startDatetime`);
    if (ticketStart && date >= ticketStart) {
      alert('Presale start date must be before ticket sale start date.');
      return;
    }
    setValue(`tickets.0.presaleStartDatetime`, date);
  };

  const handlePresaleEndDateConfirm = (date: Date) => {
    const start = watch(`tickets.0.presaleStartDatetime`);
    if (date > eventEnd) {
      alert('Presale end date must before the end of event.');
      return;
    }
    if (date < new Date()) {
      alert('Presale end date cannot be in the past.');
      return;
    }
    if (!start || date <= start) {
      alert('Presale end date must be after sale start date.');
      return;
    }
    const ticketStart = watch(`tickets.0.startDatetime`);
    if (ticketStart && date >= ticketStart) {
      alert('Presale end date must be before ticket sale start date.');
      return;
    }
    setValue(`tickets.0.presaleEndDatetime`, date);
  };

  const [timelineModal, setTimelineModal] = useState(false);
  const handleAddTimeline = () => {
    setTimelineModal(false);
  };

  const toggleTimeline = (checked: boolean) => {
    if (canEnable && checked) {
      setValue(`tickets.0.hasTimeline`, true);
      setTimelineModal(true);
    } else {
      setValue(`tickets.0.hasTimeline`, false);
    }
  };

  const handleTimelineModalDismiss = () => {
    if (!watch('tickets.0.startDatetime') || !watch('tickets.0.endDatetime')) {
      setValue(`tickets.0.hasTimeline`, false);
      setTimelineModal(false);
    }
  };

  const startDatetime = watch(`tickets.0.startDatetime`);

  const startDatetimeLabel = startDatetime
    ? formatDateTime(startDatetime)
    : 'Start date and time';

  const endDatetime = watch(`tickets.0.endDatetime`);

  const endDatetimeLabel = endDatetime
    ? formatDateTime(endDatetime)
    : 'End date and time';

  const presaleEnd = watch(`tickets.0.presaleEndDatetime`);

  const handleStartDateConfirm = (date: Date) => {
    if (date < new Date()) {
      alert('Ticket sale start date cannot be in the past.');
      return;
    }

    if (date >= eventEnd) {
      alert('Ticket sale start date must be before the end of event.');
      return;
    }

    if (presaleEnd && date <= presaleEnd) {
      alert('Ticket sale start date must be after the presale end date.');
      return;
    }

    setValue(`tickets.0.startDatetime`, date);
  };

  const handleEndDateConfirm = (date: Date) => {
    if (date >= eventEnd) {
      alert('Ticket sale end date must be before the end of event.');
      return;
    }
    const start = watch('tickets.0.startDatetime');

    if (!start || date <= start) {
      alert('Ticket sale end date cannot be before the start date.');
      return;
    }

    if (presaleEnd && date <= presaleEnd) {
      alert('Ticket sale end date must be after the presale end date.');
      return;
    }

    setValue('tickets.0.endDatetime', date);
  };

  const handleAddTicket = () => {
    append(watch('tickets.0'));
    update(0, defaultTicket);
    setStep(11);
  };

  // Presale purchase limit
  const [localPresalePurchaseLimit, setLocalPresalePurchaseLimit] = useState(
    watch(`tickets.0.presalePurchaseLimit`) || 10
  );

  const isContinueDisabled = useMemo(() => {
    const ticket = getValues('tickets.0');
    return (
      !fieldStates['tickets.0.price'].isValid ||
      !ticket?.name ||
      !ticket?.price ||
      !ticket?.quantity
    );
  }, [getValues, fieldStates]);

  return (
    <CreateEventLayout
      title="Set up tickets"
      subTitle="You can create multiple ticket types with varying prices."
      footer={
        <div className="flex flex-row gap-4">
          <Button
            variant="secondary"
            label="Previous"
            size="sm"
            disabled={fromStep === 13}
            onPress={() => setStep(7)}
          />
          <Button
            label="Continue"
            size="sm"
            disabled={isContinueDisabled}
            onPress={() => {
              if (fromStep === 13) {
                setStep(13);
              } else {
                handleAddTicket();
              }
            }}
          />
        </div>
      }
    >
      <div className="flex flex-col gap-4">
        <ControlledInput
          name={`tickets.0.name`}
          label="Ticket name e.g General admission, VIP"
          control={control}
        />
        <CostSelector
          control={control}
          setValue={setValue}
          name={`tickets.0.price`}
          label="Price"
          costOptions={[0, 5000, 10000, 20000, 50000]}
        />
        <ControlledInput
          name={`tickets.0.quantity`}
          label="Quantity available"
          control={control}
          type="number"
          onChange={(e) =>
            setValue('tickets.0.quantity', Number(e.target.value))
          }
        />
        <ControlledInput
          name={`tickets.0.description`}
          label="Ticket description (optional)"
          control={control}
          multiline
          rows={3}
        />
        <FeatureToggle
          title="Enable purchase limit"
          subtitle="Limit ticket per order, default uses a maximum of 10"
          checked={watch(`tickets.0.hasPurchaseLimit`)}
          onChange={(checked) =>
            setValue(`tickets.0.hasPurchaseLimit`, checked)
          }
          accessibilityLabel="Enable purchase limit"
          disabled={!canEnable}
        />
        {watch(`tickets.0.hasPurchaseLimit`) && (
          <div className="min-h-10 flex flex-col gap-4 px-4">
            <P className="text-sm text-neutral-500 dark:text-neutral-400">
              Set the maximum number of this ticket type a single buyer can
              include in their order
            </P>
            <div className="flex flex-row items-center justify-between">
              <span className="text-base font-medium dark:text-neutral-100">
                Number of allowed purchase
              </span>
              <Counter
                value={watch(`tickets.0.purchaseLimit`)}
                minimum={1}
                maximum={10}
                onValueChange={(value) =>
                  setValue(`tickets.0.purchaseLimit`, value)
                }
              />
            </div>
          </div>
        )}
        <FeatureToggle
          title="Include ticket sale timeline"
          subtitle="Set specific period for ticket sale, default uses event date and time"
          checked={watch(`tickets.0.hasTimeline`)}
          onChange={toggleTimeline}
          {...(watch(`tickets.0.hasTimeline`) &&
            startDatetime &&
            endDatetime && {
              editText: `${formatDate(startDatetime)} - ${formatDate(
                endDatetime
              )}`,
              onEditPress: () => setTimelineModal(true),
            })}
          accessibilityLabel="Include ticket sale timeline"
          disabled={!canEnable}
        />

        <FeatureToggle
          title="Offer ticket presale"
          checked={watch(`tickets.0.hasPresale`)}
          onChange={togglePresale}
          {...(watch(`tickets.0.hasPresale`) && {
            editText: 'Edit presale ticket',
            onEditPress: () => setPresaleModal(true),
          })}
          accessibilityLabel="Offer ticket presale"
          disabled={!canEnable}
        />
      </div>
      <Modal
        isOpen={presaleModal}
        onClose={handlePresaleModalDismiss}
        title="Set up presale"
      >
        <div className="flex flex-col gap-4">
          <Input
            label="Presale name"
            value={watch(`tickets.0.name`) + ' - Presale'}
            readOnly
          />
          <CostSelector
            control={control}
            setValue={setValue}
            name={`tickets.0.presalePrice`}
            label="Price"
            costOptions={[0, 5000, 10000, 20000, 50000]}
          />
          <DateTimePicker
            selected={presaleStartDatetime}
            onChange={handlePresaleStartDateConfirm}
            minDate={new Date()}
            label={presaleStartDatetimeLabel}
            maxDate={startDatetime || presaleEndDatetime}
            withPortal
          />
          <DateTimePicker
            selected={presaleEndDatetime}
            onChange={handlePresaleEndDateConfirm}
            minDate={presaleStartDatetime}
            label={presaleEndDatetimeLabel}
            maxDate={startDatetime || eventEnd}
            withPortal
          />

          <ControlledInput
            name={`tickets.0.presaleQuantity`}
            label="Quantity available"
            control={control}
            type="number"
          />
          <ControlledInput
            name={`tickets.0.presaleDescription`}
            label="Ticket description (optional)"
            multiline
            rows={3}
            control={control}
          />
          <div className="flex flex-row items-center justify-between rounded-md bg-bg-subtle-light p-3 dark:bg-bg-subtle-dark">
            <span className="text-base font-medium dark:text-neutral-100">
              Max quantity per order
            </span>

            <Counter
              value={localPresalePurchaseLimit}
              initialValue={10}
              minimum={1}
              maximum={10}
              onValueChange={(purchaseLimit) =>
                setLocalPresalePurchaseLimit(purchaseLimit)
              }
            />
          </div>
          <Button
            label="Save"
            disabled={
              !watch(`tickets.0.presaleEndDatetime`) ||
              !watch(`tickets.0.presaleStartDatetime`) ||
              !watch(`tickets.0.presalePrice`) ||
              !watch(`tickets.0.presaleQuantity`)
            }
            onPress={handleAddPresale}
          />
        </div>
      </Modal>
      <Modal
        isOpen={timelineModal}
        onClose={handleTimelineModalDismiss}
        title="Set up ticket sale timeline"
      >
        <div className="flex flex-col gap-4">
          <P className="text-sm text-neutral-500 dark:text-neutral-400">
            Set specific period for this ticket type sale
          </P>
          <DateTimePicker
            selected={startDatetime}
            onChange={handleStartDateConfirm}
            minDate={new Date()}
            label={startDatetimeLabel}
            withPortal
          />
          <DateTimePicker
            selected={endDatetime}
            onChange={handleEndDateConfirm}
            minDate={startDatetime}
            label={endDatetimeLabel}
            withPortal
          />
          <Button
            label="Save"
            className="mt-auto"
            disabled={
              !watch(`tickets.0.startDatetime`) ||
              !watch(`tickets.0.endDatetime`)
            }
            onPress={handleAddTimeline}
          />
        </div>
      </Modal>
    </CreateEventLayout>
  );
}
