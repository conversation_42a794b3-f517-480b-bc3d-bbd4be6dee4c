'use client';
import { useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { CreateEventLayout } from '@/components/layouts';
import {
  Button,
  ControlledInput,
  CostSelector,
  DateTimePicker,
  FeatureToggle,
  Input,
  Modal,
  P,
} from '@/components/ui';
import { type CreateEventFormType, useFieldBlurAndFilled } from '@/lib';
import { formatDate, formatDateTime } from '@/lib';
import { Counter } from '@/components/ui/counter';
import { type StepProps } from '../page';

export default function EditTicket({ setStep, ticketId }: StepProps) {
  const { watch, control, setValue, getValues } =
    useFormContext<CreateEventFormType>();

  const ticketIndex = watch('tickets')?.findIndex(
    (ticket) => ticket.id === ticketId
  );

  const { fieldStates } = useFieldBlurAndFilled<CreateEventFormType>([
    `tickets.${ticketIndex}.name`,
    `tickets.${ticketIndex}.price`,
    `tickets.${ticketIndex}.quantity`,
    `tickets.${ticketIndex}.presalePrice`,
    `tickets.${ticketIndex}.presaleQuantity`,
    `tickets.${ticketIndex}.presaleStartDatetime`,
    `tickets.${ticketIndex}.presaleEndDatetime`,
    `tickets.${ticketIndex}.purchaseLimit`,
    `tickets.${ticketIndex}.description`,
    `tickets.${ticketIndex}.startDatetime`,
    `tickets.${ticketIndex}.endDatetime`,
  ]);

  // can enable all features
  const name = watch(`tickets.${ticketIndex}.name`);
  const price = watch(`tickets.${ticketIndex}.price`);
  const quantity = watch(`tickets.${ticketIndex}.quantity`);
  const canEnable = name && price && quantity;

  const [presaleModal, setPresaleModal] = useState(false);
  function enablePresale() {
    setValue(`tickets.${ticketIndex}.hasPresale`, true);
    setValue(`tickets.${ticketIndex}.presalePrice`, '');
    setValue(`tickets.${ticketIndex}.presaleQuantity`, 0);
    setValue(`tickets.${ticketIndex}.presaleDescription`, '');
  }

  function disablePresale() {
    if (watch(`tickets.${ticketIndex}.hasPresale`)) {
      setValue(`tickets.${ticketIndex}.hasPresale`, false, {
        shouldValidate: true,
      });
    }
  }

  const togglePresale = (checked: boolean) => {
    if (checked) {
      enablePresale();
      setPresaleModal(true);
    } else {
      disablePresale();
      setPresaleModal(false);
    }
  };

  const handleAddPresale = () => {
    setPresaleModal(false);
  };

  const handlePresaleModalDismiss = () => {
    if (
      !watch(`tickets.${ticketIndex}.presalePrice`) ||
      !watch(`tickets.${ticketIndex}.presaleStartDatetime`) ||
      !watch(`tickets.${ticketIndex}.presaleEndDatetime`) ||
      !watch(`tickets.${ticketIndex}.presaleQuantity`)
    ) {
      disablePresale();
      setPresaleModal(false);
    }
  };

  const eventEnd = watch('endDatetime');

  // presale datetimes

  const presaleStartDatetime = watch(
    `tickets.${ticketIndex}.presaleStartDatetime`
  );

  const presaleStartDatetimeLabel = presaleStartDatetime
    ? formatDateTime(presaleStartDatetime)
    : 'Start date and time';

  const presaleEndDatetime = watch(`tickets.${ticketIndex}.presaleEndDatetime`);

  const presaleEndDatetimeLabel = presaleEndDatetime
    ? formatDateTime(presaleEndDatetime)
    : 'End date and time';

  const ticketStart = watch(`tickets.${ticketIndex}.startDatetime`);

  const handlePresaleStartDateConfirm = (date: Date) => {
    if (date < new Date()) {
      alert('Presale start date cannot be in the past.');
      return;
    }
    if (date >= eventEnd) {
      alert('Presale start date must before the end of event.');
      return;
    }
    if (ticketStart && date >= ticketStart) {
      alert('Presale start date must be before ticket sale start date.');
      return;
    }
    setValue(`tickets.${ticketIndex}.presaleStartDatetime`, date);
  };

  const handlePresaleEndDateConfirm = (date: Date) => {
    const start = watch(`tickets.${ticketIndex}.presaleStartDatetime`);
    if (date > eventEnd) {
      alert('Presale end date must before the end of event.');
      return;
    }
    if (date < new Date()) {
      alert('Presale end date cannot be in the past.');
      return;
    }
    if (!start || date <= start) {
      alert('Presale end date must be after sale start date.');
      return;
    }
    if (ticketStart && date >= ticketStart) {
      alert('Presale sale end date must be before the ticket sale start date.');
      return;
    }
    setValue(`tickets.${ticketIndex}.presaleEndDatetime`, date);
  };

  //timeline

  const [timelineModal, setTimelineModal] = useState(false);

  const handleAddTimeline = () => {
    setTimelineModal(false);
  };

  const handleTimelineModalDismiss = () => {
    if (
      !watch(`tickets.${ticketIndex}.startDatetime`) ||
      !watch(`tickets.${ticketIndex}.endDatetime`)
    ) {
      setValue(`tickets.${ticketIndex}.hasTimeline`, false);
      setTimelineModal(false);
    }
  };

  const toggleTimeline = (checked: boolean) => {
    if (canEnable && checked) {
      setValue(`tickets.${ticketIndex}.hasTimeline`, true);
      setTimelineModal(true);
    } else {
      setValue(`tickets.${ticketIndex}.hasTimeline`, false);
    }
  };

  // timeline datetimes

  const startDatetime = watch(`tickets.${ticketIndex}.startDatetime`);

  const startDatetimeLabel = startDatetime
    ? formatDateTime(startDatetime)
    : 'Start date and time';

  const endDatetime = watch(`tickets.${ticketIndex}.endDatetime`);

  const endDatetimeLabel = endDatetime
    ? formatDateTime(endDatetime)
    : 'End date and time';

  const presaleEnd = watch(`tickets.${ticketIndex}.presaleEndDatetime`);

  const handleStartDateConfirm = (date: Date) => {
    if (date < new Date()) {
      alert('Ticket sale start date cannot be in the past.');
      return;
    }

    if (date >= eventEnd) {
      alert('Ticket sale start date must be before the end of event.');
      return;
    }

    if (presaleEnd && date <= presaleEnd) {
      alert('Ticket sale start date must be after the presale end date.');
      return;
    }
    setValue(`tickets.${ticketIndex}.startDatetime`, date);
  };

  const handleEndDateConfirm = (date: Date) => {
    if (date >= eventEnd) {
      alert('Ticket sale end date must be before the end of event.');
      return;
    }
    const start = watch(`tickets.${ticketIndex}.startDatetime`);
    if (!start || date <= start) {
      alert('Ticket sale end date cannot be before the start date.');
      return;
    }

    if (presaleEnd && date <= presaleEnd) {
      alert('Ticket sale end date must be after the presale end date.');
      return;
    }
    setValue(`tickets.${ticketIndex}.endDatetime`, date);
  };

  // Presale purchase limit
  //Purchase limit
  const [localPresalePurchaseLimit, setLocalPresalePurchaseLimit] = useState(
    watch(`tickets.${ticketIndex}.presalePurchaseLimit`) || 10
  );
  const isContinueDisabled = useMemo(() => {
    const ticket = getValues(`tickets.${ticketIndex}`);
    return (
      !fieldStates[`tickets.${ticketIndex}.price`].isValid ||
      !ticket?.name ||
      !ticket?.price ||
      !ticket?.quantity
    );
  }, [getValues, fieldStates, ticketIndex]);

  if (ticketIndex === -1) return;

  return (
    <CreateEventLayout
      title="Edit Ticket"
      subTitle="Make changes to each ticket category."
      footer={
        <Button
          label="Update Ticket"
          disabled={isContinueDisabled}
          onPress={() => setStep(11)}
        />
      }
    >
      <div className="flex flex-col gap-4">
        <ControlledInput
          name={`tickets.${ticketIndex}.name`}
          label="Ticket name e.g General admission, VIP"
          control={control}
        />
        <CostSelector
          control={control}
          setValue={setValue}
          name={`tickets.${ticketIndex}.price`}
          label="Price"
          costOptions={[0, 5000, 10000, 20000, 50000]}
        />
        <ControlledInput
          name={`tickets.${ticketIndex}.quantity`}
          label="Quantity available"
          type="number"
          control={control}
          onChange={(e) =>
            setValue(`tickets.${ticketIndex}.quantity`, Number(e.target.value))
          }
        />
        <ControlledInput
          name={`tickets.${ticketIndex}.description`}
          label="Ticket description (optional)"
          control={control}
          multiline
          rows={3}
        />
        <FeatureToggle
          title="Enable purchase limit"
          subtitle="Limit ticket per order, default uses a maximum of 10"
          checked={watch(`tickets.${ticketIndex}.hasPurchaseLimit`)}
          onChange={(checked) =>
            setValue(`tickets.${ticketIndex}.hasPurchaseLimit`, checked)
          }
          accessibilityLabel="Enable purchase limit"
          disabled={!canEnable}
        />
        {watch(`tickets.${ticketIndex}.hasPurchaseLimit`) && (
          <div className="min-h-10 flex flex-col gap-4 px-4">
            <P className="text-sm text-neutral-500 dark:text-neutral-400">
              Set the maximum number of this ticket type a single buyer can
              include in their order
            </P>
            <div className="flex flex-row items-center justify-between">
              <span className="text-base font-medium dark:text-neutral-100">
                Number of allowed purchase
              </span>
              <Counter
                value={watch(`tickets.${ticketIndex}.purchaseLimit`)}
                minimum={1}
                maximum={10}
                onValueChange={(value) =>
                  setValue(`tickets.${ticketIndex}.purchaseLimit`, value)
                }
              />
            </div>
          </div>
        )}
        <FeatureToggle
          title="Include ticket sale timeline"
          subtitle="Set specific period for ticket sale, default uses event date and time"
          checked={watch(`tickets.${ticketIndex}.hasTimeline`)}
          onChange={toggleTimeline}
          {...(watch(`tickets.${ticketIndex}.hasTimeline`) &&
            startDatetime &&
            endDatetime && {
              editText: `${formatDate(startDatetime)} - ${formatDate(endDatetime)}`,
              onEditPress: () => setTimelineModal(true),
            })}
          accessibilityLabel="Include ticket sale timeline"
          disabled={!canEnable}
        />
        <FeatureToggle
          title="Offer ticket presale"
          checked={watch(`tickets.${ticketIndex}.hasPresale`)}
          onChange={togglePresale}
          accessibilityLabel="Offer ticket presale"
          {...(watch(`tickets.${ticketIndex}.hasPresale`) && {
            editText: 'Edit presale ticket',
            onEditPress: () => setPresaleModal(true),
          })}
        />
      </div>
      <Modal
        isOpen={presaleModal}
        onClose={handlePresaleModalDismiss}
        title="Set up presale"
      >
        <div className="flex flex-col gap-4">
          <Input
            label="Presale name"
            value={watch(`tickets.${ticketIndex}.name`) + ' - Presale'}
            readOnly
          />

          <CostSelector
            name={`tickets.${ticketIndex}.presalePrice`}
            setValue={setValue}
            label="Price"
            costOptions={[0, 5000, 10000, 20000, 50000]}
            control={control}
          />
          <DateTimePicker
            selected={presaleStartDatetime}
            onChange={handlePresaleStartDateConfirm}
            minDate={new Date()}
            label={presaleStartDatetimeLabel}
            maxDate={startDatetime || presaleEndDatetime}
            withPortal
          />
          <DateTimePicker
            selected={presaleEndDatetime}
            onChange={handlePresaleEndDateConfirm}
            minDate={presaleStartDatetime}
            label={presaleEndDatetimeLabel}
            maxDate={startDatetime || eventEnd}
            withPortal
          />
          <ControlledInput
            name={`tickets.${ticketIndex}.presaleQuantity`}
            label="Quantity available"
            control={control}
            type="number"
          />
          <ControlledInput
            name={`tickets.${ticketIndex}.presaleDescription`}
            label="Ticket description (optional)"
            control={control}
          />
          <div className="flex flex-row items-center justify-between rounded-md bg-bg-subtle-light p-3 dark:bg-bg-subtle-dark">
            <span className="text-base font-medium dark:text-neutral-100">
              Max quantity per order
            </span>

            <Counter
              value={localPresalePurchaseLimit}
              initialValue={10}
              minimum={1}
              maximum={10}
              onValueChange={(purchaseLimit) =>
                setLocalPresalePurchaseLimit(purchaseLimit)
              }
            />
          </div>
          <Button
            label="Save"
            disabled={
              !watch(`tickets.${ticketIndex}.presaleEndDatetime`) ||
              !watch(`tickets.${ticketIndex}.presaleStartDatetime`) ||
              !watch(`tickets.${ticketIndex}.presalePrice`) ||
              !watch(`tickets.${ticketIndex}.presaleQuantity`)
            }
            onPress={handleAddPresale}
          />
        </div>
      </Modal>

      <Modal
        isOpen={timelineModal}
        onClose={handleTimelineModalDismiss}
        title="Set up ticket sale timeline"
      >
        <div className="flex flex-col gap-4">
          <P className="text-sm text-neutral-500 dark:text-neutral-400">
            Set specific period for this ticket type sale
          </P>
          <DateTimePicker
            selected={startDatetime}
            onChange={handleStartDateConfirm}
            minDate={new Date()}
            label={startDatetimeLabel}
            withPortal
          />
          <DateTimePicker
            selected={endDatetime}
            onChange={handleEndDateConfirm}
            minDate={startDatetime}
            label={endDatetimeLabel}
            withPortal
          />
          <Button
            label="Save"
            className="mt-auto"
            disabled={
              !watch(`tickets.${ticketIndex}.startDatetime`) ||
              !watch(`tickets.${ticketIndex}.endDatetime`)
            }
            onPress={handleAddTimeline}
          />
        </div>
      </Modal>
    </CreateEventLayout>
  );
}
