'use client';
import dayjs from 'dayjs';
import React from 'react';

import { type IEvent, useGetEvents } from '@/api/events';
import { CreatorEventsTab } from '@/components/tab-views/my-events-tab';
import { AppTab } from '@/components/ui';
import { useAuth } from '@/lib';
import { type TabScreenItem } from '@/types';

export default function MyEvents() {
  const { user: currentUser, isLoading: userLoading } = useAuth();
  const [index, setIndex] = React.useState(0);

  const { data: eventData, isPending } = useGetEvents({
    variables: {
      userId: currentUser?.id,
      organizerId: currentUser?.id,
    },
  });

  const { upcomingEvents, pastEvents } = React.useMemo(() => {
    if (!eventData) return { upcomingEvents: [], pastEvents: [] };
    const { events } = eventData;
    const now = dayjs();

    const upcomingEvents: IEvent[] = [];
    const pastEvents: IEvent[] = [];

    events.forEach((event) => {
      const eventEndTime = dayjs(event.endTime);

      if (eventEndTime.isAfter(now)) {
        upcomingEvents.push(event);
      } else {
        pastEvents.push(event);
      }
    });

    return { upcomingEvents, pastEvents };
  }, [eventData]);

  const tabItems: TabScreenItem[] = React.useMemo(() => {
    return [
      {
        key: 'Upcoming',
        title: 'Upcoming',
        component: () => (
          <CreatorEventsTab
            events={upcomingEvents}
            index={0}
            isPending={isPending || userLoading}
          />
        ),
      },
      {
        key: 'Past',
        title: 'Past',
        component: () => (
          <CreatorEventsTab
            events={pastEvents}
            index={1}
            isPending={isPending || userLoading}
          />
        ),
      },
    ];
  }, [upcomingEvents, pastEvents, isPending, userLoading]);

  return (
    <div className="flex-1 ">
      <AppTab
        items={tabItems}
        tabIndex={index}
        tabSetIndex={(i) => setIndex(i)}
      />
    </div>
  );
}
