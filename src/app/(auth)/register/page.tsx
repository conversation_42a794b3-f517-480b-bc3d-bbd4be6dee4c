'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSearchParams, useRouter } from 'next/navigation';
import { useState } from 'react';
import { useForm, useFormContext } from 'react-hook-form';
import { toast } from 'react-hot-toast';

import {
  type AccountCreationPayload,
  useCreateAccount,
  useRegistrationRequestOtp,
  useValidateOtp,
} from '@/api/auth';
import { AuthLayout } from '@/components/layouts/auth-layout';
import { Button } from '@/components/ui';
import {
  DEFAULT_COORDINATES,
  defaultLocationObject,
  type FormType as AccountSetupFormType,
  getLatLongFromCountry,
  useAuth,
  useFieldBlurAndFilled,
} from '@/lib';

import AccountSelection from './account-selection';
import Country from './country';
import Email from './email';
import Name from './name';
import Password from './password';
import Username from './username';
import VerifyEmail from './verify-email';
import { type FormType as OtpFormType, OtpSchema } from './verify-email';

export default function AuthFlow() {
  const searchParams = useSearchParams();

  const registerType = searchParams.get('registerType') as
    | 'social'
    | 'email'
    | null;

  const socialEmail = searchParams.get('socialEmail') ?? undefined;
  const isSocialSignup = registerType === 'social';
  const [step, setStep] = useState(0);

  const nextStep = () => setStep((s) => Math.min(s + 1, steps.length - 1));
  const prevStep = () => setStep((s) => Math.max(s - 1, 0));

  const router = useRouter();
  const { login } = useAuth();
  const { watch } = useFormContext<AccountSetupFormType>();
  const { fieldStates } = useFieldBlurAndFilled<AccountSetupFormType>([
    'email',
    'firstName',
    'lastName',
    'username',
    'country',
    'password',
    'confirmPassword',
  ]);

  const {
    control: controlOtp,
    getFieldState,
    formState,
    watch: watchOtp,
  } = useForm<OtpFormType>({
    resolver: zodResolver(OtpSchema),
    defaultValues: {
      code: '',
    },
    mode: 'onChange',
  });

  const { mutate: requestOtp, isPending } = useRegistrationRequestOtp();
  const { mutate: validateOtp, isPending: isValidating } = useValidateOtp();
  const email = watch('email');
  const { invalid } = getFieldState('code', formState);
  const otp = watchOtp('code');
  const isValid = !!otp && !invalid;

  const { mutate: createAccount, isPending: isCreatingPending } =
    useCreateAccount({
      onSuccess: ({ user }) => {
        toast.success('Account setup successful', {
          position: 'top-center',
        });
        login({ email: user.email, password: watch('password') });
        router.replace('/events');
      },
      onError: (error) => toast.error(error.message),
    });

  const handleSubmit = async () => {
    const { role, email, password, firstName, lastName, username, country } =
      watch();

    const countryCoords =
      // Platform.OS === 'android'
      //   ? DEFAULT_COORDINATES[country]
      await getLatLongFromCountry(country);

    const payload: AccountCreationPayload = {
      email: socialEmail || email,
      fullName: `${firstName} ${lastName}`,
      password,
      role,
      username,
      location: {
        ...defaultLocationObject,
        country: (country as string) ?? 'Nigeria',
        coordinates: {
          lat: countryCoords?.latitude || 0,
          lng: countryCoords?.longitude || 0,
          latitude: countryCoords?.latitude || 0,
          longitude: countryCoords?.longitude || 0,
        },
      },
      isSocialSetup: isSocialSignup,
    };

    createAccount(payload);
  };

  const onValidateEmail = () => {
    validateOtp(
      { email, otp },
      {
        onSuccess: () => {
          toast.success('Email verified successfully', {
            position: 'top-center',
          });
          nextStep();
        },
        onError: (error) => toast.error(error.message),
      }
    );
  };

  const steps = [
    {
      title: 'What Brings You to Popla?',
      content: <AccountSelection />,
      footer: (
        <Button
          label="Continue"
          className="my-4"
          disabled={!watch('role')}
          onPress={() => (isSocialSignup ? setStep(4) : nextStep())}
        />
      ),
    },
    {
      title: 'What’s your email?',
      subTitle: 'We will send your account information here.',
      content: <Email />,
      footer: (
        <Button
          label="Continue"
          className="my-4"
          disabled={!fieldStates.email.isValid || isPending}
          loading={isPending}
          onPress={() => {
            requestOtp(
              { email },
              {
                onSuccess: () => {
                  toast.success('OTP sent successfully', {
                    position: 'top-center',
                  });
                  nextStep();
                },
                onError: (error) => toast.error(error.message),
              }
            );
          }}
        />
      ),
    },
    {
      title: '',
      content: <VerifyEmail control={controlOtp} />,
      footer: (
        <Button
          label="Continue"
          className="my-4"
          disabled={!isValid || isValidating}
          loading={isValidating}
          onPress={onValidateEmail}
        />
      ),
    },
    {
      title: 'Let’s secure your account',
      subTitle: 'Create a password.',
      content: <Password />,
      footer: (
        <Button
          label="Continue"
          className="my-4"
          disabled={
            !fieldStates.password.isValid ||
            !fieldStates.confirmPassword.isValid
          }
          onPress={nextStep}
        />
      ),
    },
    {
      title: 'What should we call you?',
      subTitle: 'We just need a few details to personalize your experience.',
      content: <Name />,
      footer: (
        <Button
          label="Continue"
          className="my-4"
          disabled={
            !fieldStates.firstName.isValid || !fieldStates.lastName.isValid
          }
          onPress={nextStep}
        />
      ),
    },
    {
      title: 'Choose your Popla username',
      subTitle: 'This is how you’ll appear on Popla. Make it unique and fun!',
      content: <Username />,
      footer: (
        <Button
          label="Continue"
          className="my-4"
          disabled={!fieldStates.username.isValid}
          onPress={nextStep}
        />
      ),
    },
    {
      title: 'Where are you located?',
      subTitle: 'This will help Popla streamline your experience.',
      content: <Country />,
      footer: (
        <Button
          label="Create Account"
          className="my-4"
          disabled={!watch('country') || isCreatingPending}
          onPress={handleSubmit}
          loading={isCreatingPending}
        />
      ),
    },
  ];

  return (
    <AuthLayout
      title={steps[step].title}
      subTitle={steps[step].subTitle}
      pagination={{
        activeStep: step + 1,
        totalSteps: steps.length,
      }}
      onBackPress={() => {
        if (step === 0) {
          router.back();
        } else {
          prevStep();
        }
      }}
      footer={steps[step].footer}
    >
      {steps[step].content}
    </AuthLayout>
  );
}
