'use client';
import React, { useState } from 'react';
import { useFormContext } from 'react-hook-form';

import { colors, Image, Modal, P, CaretDown, Tick } from '@/components/ui';
import {
  type FormType as AccountSetupFormType,
  SUPPORTED_COUNTRIES,
} from '@/lib';

export default function Country() {
  const { watch, setValue } = useFormContext<AccountSetupFormType>();

  const [countryModal, setCountryModal] = useState(false);

  const selectedCountry = watch('country');

  const countryLabel = SUPPORTED_COUNTRIES.find(
    (country) => country.value === selectedCountry
  )?.label;

  const onSelect = (selectedCountry: string) => {
    setValue('country', selectedCountry as string);
  };

  return (
    <div className="flex flex-col gap-4">
      <div>
        <button
          className="h-12 flex flex-row items-center justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark w-full"
          onClick={() => setCountryModal(true)}
        >
          <div className="flex flex-row items-center gap-x-1">
            {selectedCountry && (
              <Image
                src={`https://flagcdn.com/128x96/${
                  SUPPORTED_COUNTRIES.find((c) => c.value === selectedCountry)
                    ?.code
                }.png`}
                fill
                className="size-6 rounded-xl"
                alt="Country flag"
              />
            )}
            <P
              className={
                countryLabel
                  ? 'dark:text-neutral-100'
                  : 'text-neutral-400 dark:text-neutral-500'
              }
            >
              {countryLabel ?? 'Select Country'}
            </P>
          </div>
          <CaretDown color={colors.grey[70]} />
        </button>
      </div>

      <Modal isOpen={countryModal} onClose={() => setCountryModal(false)}>
        <div className="px-4 pb-4">
          {SUPPORTED_COUNTRIES.map((country) => (
            <button
              key={country.value}
              className="flex flex-row items-center border-b border-neutral-300 dark:border-neutral-700 w-full py-2"
              onClick={() => {
                setCountryModal(false);
                onSelect(country.value);
              }}
            >
              <div className="p-2">
                <Image
                  src={`https://flagcdn.com/128x96/${country.code}.png`}
                  className="size-10 gap-2 rounded-xl"
                  alt={`${country.label} flag`}
                  width={40}
                  height={40}
                />
              </div>

              <P className="flex-1 dark:text-neutral-100">{country.label}</P>
              {selectedCountry === country.value && (
                <Tick color={colors.brand['60']} />
              )}
            </button>
          ))}
        </div>
      </Modal>
    </div>
  );
}
