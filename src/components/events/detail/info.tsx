'use client';
import { isPast } from 'date-fns';
import { useRouter } from 'next/navigation';
import React from 'react';
import { useTheme } from 'next-themes';
import { EventFormat, EventType, type ISingleEvent } from '@/api/events';
import {
  Button,
  colors,
  H5,
  KeyIcon,
  MapIcon,
  MdBoldLabel,
  Pencil,
  semanticColors,
  Small,
  SmRegularLabel,
  Tiny,
  Image,
  XsBoldLabel,
} from '@/components/ui';
import { cn, formatEventDateTime } from '@/lib';
import { FiCalendar, FiMapPin } from 'react-icons/fi';
import { FaCalendarPlus } from 'react-icons/fa';
import { CollaboratorCard } from '@/components/events';
import { IoAdd } from 'react-icons/io5';

interface LocationInfoProps {
  event: ISingleEvent;
  isCreator: boolean;
}

const LocationInfo: React.FC<LocationInfoProps> = ({ event, isCreator }) => {
  if (event?.eventFormat === EventFormat.IN_PERSON && event?.location) {
    const eventAddress =
      event.location.landmark ||
      event.location.address.replace(/^CCQH\+9HP\s*,?\s*/, '').trim();
    return (
      <div className="flex flex-col gap-1">
        <H5 className="trunacate">{eventAddress}</H5>
        <Tiny className="text-grey-60 dark:text-grey-50">
          {event.location.address}
        </Tiny>
      </div>
    );
  }

  if (event?.eventFormat === EventFormat.HYBRID && event?.location) {
    const eventAddress =
      event.location.landmark ||
      event.location.address.replace(/^CCQH\+9HP\s*,?\s*/, '').trim();
    return (
      <>
        <div className="flex flex-col gap-1 pb-2">
          <H5>{eventAddress}</H5>
          <Tiny className="text-grey-60 dark:text-grey-50">
            {event.location.address}
          </Tiny>
        </div>

        {isCreator && event.onlineEventUrl && (
          <div className="flex flex-col gap-1">
            <H5>Event URL</H5>
            <Tiny className="text-grey-60 dark:text-grey-50">
              {event.onlineEventUrl}
            </Tiny>
          </div>
        )}
      </>
    );
  }

  return (
    <div className="gap-1">
      <H5 className="text-grey-60 dark:text-grey-50">Location not specified</H5>
    </div>
  );
};

interface Props {
  event: ISingleEvent;
  isCreator: boolean;
  addToCalendar: () => void;
  openInMaps: (address: string) => void;
  handleEventEdit: (type: 'desc' | 'date' | 'location') => void;
  setConfirmRAVisible: React.Dispatch<React.SetStateAction<boolean>>;
  setArtistToDelete: React.Dispatch<React.SetStateAction<string | null>>;
}

export const EventInfo: React.FC<Props> = ({
  event,
  isCreator,
  addToCalendar,
  openInMaps,
  handleEventEdit,
  setConfirmRAVisible,
  setArtistToDelete,
}) => {
  const { push } = useRouter();
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';
  const hasEventEnded = event?.endTime ? isPast(event.endTime) : true;

  const dateTimeLabel = formatEventDateTime(event?.startTime, event?.endTime);
  const calendarColor = isDark ? colors.brand[40] : colors.brand[70];
  const pinColor = isDark ? colors.white : colors.grey[100];

  return (
    <React.Fragment>
      {event?.eventType === EventType.PRIVATE && isCreator && (
        <div className="flex flex-row justify-between gap-3">
          <div className="flex flex-row items-center gap-3">
            <KeyIcon />
            <div className="flex flex-col gap-1">
              <H5>Access code</H5>
              <Tiny className="text-grey-60 dark:text-grey-50">
                See list of access codes
              </Tiny>
            </div>
          </div>
          <Button
            label="View codes"
            size="xs"
            className="h-8"
            onPress={() => {
              push(
                `/events/${event.id}/access?slug=${event.slug}&title=${encodeURIComponent(event.title)}`
              );
            }}
            variant="outline"
          />
        </div>
      )}
      {/* Date Row */}
      <div
        className={cn(
          'flex flex-row justify-between gap-3',
          !isCreator && 'flex-col gap-2'
        )}
      >
        <div className="flex flex-row items-center gap-3">
          <FiCalendar name="calendar" size={24} color="white dark:invert" />
          <div className="flex flex-col gap-1">
            <H5>{dateTimeLabel}</H5>
            {!isCreator && (
              <Tiny className="text-grey-60 dark:text-grey-50">
                Times are displayed in your local timezone
              </Tiny>
            )}
          </div>
        </div>
        {isCreator ? (
          <Button
            label="Edit"
            className="w-[62px]"
            size="xs"
            iconPosition="right"
            disabled={hasEventEnded}
            onPress={() => {
              handleEventEdit('date');
            }}
            variant="outline"
            iconClassName="right-2"
            icon={
              <Pencil
                color={
                  isDark
                    ? semanticColors.accent.bold.dark
                    : semanticColors.accent.bold.light
                }
              />
            }
          />
        ) : (
          <button
            className="ml-9 w-auto mx-auto flex flex-row items-center gap-1 rounded-full border border-accent-bold-light dark:border-accent-bold-dark p-2 pr-3 cursor-pointer hover:bg-accent-bold-light hover:text-white disabled:cursor-default disabled:bg-transparent disabled:border-border-muted-light disabled:dark:border-border-muted-dark disabled:text-fg-muted-light disabled:dark:text-fg-muted-light"
            disabled={hasEventEnded}
            onClick={addToCalendar}
          >
            <FaCalendarPlus size={16} color={calendarColor} />
            <XsBoldLabel className="text-brand-70 dark:text-brand-40">
              Add to Calendar
            </XsBoldLabel>
          </button>
        )}
      </div>

      {/* Location Row */}
      <div
        className={cn(
          'flex flex-row justify-between gap-3 items-center',
          !isCreator && 'flex-col gap-2 items-start'
        )}
      >
        <div className="flex flex-row items-center gap-3">
          {event?.eventFormat !== EventFormat.ONLINE && (
            <FiMapPin size={24} color={pinColor} />
          )}
          <div className="gap-2">
            <LocationInfo event={event} isCreator={isCreator} />
          </div>
        </div>
        {isCreator ? (
          <Button
            label="Edit"
            className="w-[62px]"
            size="xs"
            iconPosition="right"
            disabled={hasEventEnded}
            onPress={() => {
              handleEventEdit('location');
            }}
            variant="outline"
            iconClassName="right-2"
            icon={
              <Pencil
                color={
                  isDark
                    ? semanticColors.accent.bold.dark
                    : semanticColors.accent.bold.light
                }
              />
            }
          />
        ) : (
          <div className="flex-row gap-2">
            {(event?.eventFormat === EventFormat.IN_PERSON ||
              event?.eventFormat === EventFormat.HYBRID) &&
              event?.location && (
                <button
                  className="ml-9 w-[113px] flex flex-row items-center gap-1 border border-accent-bold-light dark:border-accent-bold-dark rounded-full hover:bg-accent-bold-light hover:text-white p-2 pr-3 cursor-pointer"
                  onClick={() => {
                    openInMaps(event.location.address);
                  }}
                >
                  <MapIcon color={calendarColor} />
                  <XsBoldLabel className="text-brand-70 dark:text-brand-40">
                    View on map
                  </XsBoldLabel>
                </button>
              )}
          </div>
        )}
      </div>

      {/* Organizer Info */}
      <div className="flex flex-col gap-2">
        <H5 className="text-grey-60 dark:text-grey-50">About this event</H5>
        <Small className="break-words">{event?.description}</Small>
        {isCreator && (
          <Button
            label="Edit"
            className="w-[62px]"
            size="xs"
            iconPosition="right"
            disabled={hasEventEnded}
            variant="outline"
            iconClassName="right-2"
            icon={
              <Pencil
                color={
                  isDark
                    ? semanticColors.accent.bold.dark
                    : semanticColors.accent.bold.light
                }
              />
            }
            onPress={() => handleEventEdit('desc')}
          />
        )}
      </div>
      {(event?.artists?.length ?? 0) > 0 && (
        <>
          <H5 className="text-grey-60 dark:text-grey-50">Lineup</H5>
          <div
            className="flex flex-row gap-3 items-center overflow-x-auto"
            style={{
              paddingBottom: 12,
              paddingRight: 12,
              paddingTop: 4,
            }}
          >
            {event?.artists?.map((artist, index) => (
              <CollaboratorCard
                key={index}
                artist={artist}
                onRemove={() => {
                  setArtistToDelete(artist.id);
                  setConfirmRAVisible(true);
                }}
                isCreator={isCreator}
                hasEventEnded={hasEventEnded}
              />
            ))}
            {isCreator && !hasEventEnded && (
              <button
                className="size-10 flex items-center justify-center rounded-full bg-accent-subtle-light dark:bg-accent-subtle-dark"
                onClick={() =>
                  push(
                    `${'/events/create/add-collaborators'}?isEdit=true&eventSlug=${event?.slug}&eventId=${event?.id}`
                  )
                }
              >
                <IoAdd
                  size={24}
                  color={isDark ? colors.brand[40] : colors.brand[70]}
                />
              </button>
            )}
          </div>
        </>
      )}
      {!isCreator && (
        <button
          className="flex flex-row items-center gap-4 mt-2"
          onClick={() => push(`/users/${event?.organizer.id}`)}
        >
          <Image
            src={event?.organizer.profileImageUrl || '/user.png'}
            alt="Organizer"
            width={40}
            height={40}
            className="rounded-full"
          />
          <div className="flex flex-col items-start gap-2">
            <MdBoldLabel>{event?.organizer.fullName}</MdBoldLabel>
            <SmRegularLabel className="text-fg-muted-light dark:text-fg-muted-dark text-sm">
              Organizer
            </SmRegularLabel>
          </div>
        </button>
      )}
    </React.Fragment>
  );
};
