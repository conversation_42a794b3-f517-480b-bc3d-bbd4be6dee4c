'use client';

import { useEffect, useState, useCallback } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import { cn } from '@/lib/utils';
import { useSearchEvents, useGetUpcomingEvents } from '@/api';
import { EventCardWithAction } from '@/components/cards';

interface EventCarouselProps {
  autoPlay?: boolean;
  autoPlayInterval?: number;
  type?: 'featured' | 'upcoming';
}

export const EventCarousel = ({
  autoPlay = true,
  autoPlayInterval = 9000,
  type = 'featured',
}: EventCarouselProps) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: true,
    align: 'center',
  });
  const [current, setCurrent] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  const { data: eventsData, isLoading } = useSearchEvents({
    variables: { skip: 0, take: 8, isFeatured: type === 'featured' },
    enabled: type === 'featured',
  });

  const { data: upcomingEventData, isLoading: isLoadingUpcoming } =
    useGetUpcomingEvents({
      variables: { skip: 0, take: 8 },
      enabled: type === 'upcoming',
    });

  const events =
    type === 'upcoming'
      ? (upcomingEventData?.events ?? [])
      : (eventsData?.events ?? []);

  useEffect(() => {
    if (!emblaApi) return;
    setCurrent(emblaApi.selectedScrollSnap());

    const onSelect = () => setCurrent(emblaApi.selectedScrollSnap());
    emblaApi.on('select', onSelect);

    return () => {
      emblaApi.off('select', onSelect);
    };
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi || !autoPlay || isHovered) return;

    const interval = setInterval(() => {
      emblaApi.scrollNext();
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [emblaApi, autoPlay, autoPlayInterval, isHovered]);

  const handleDotClick = useCallback(
    (index: number) => emblaApi?.scrollTo(index),
    [emblaApi]
  );

  if (isLoading || isLoadingUpcoming) {
    const placeholderCount = 3;

    return (
      <div className="py-2 w-full">
        <div className="flex overflow-hidden">
          {Array.from({ length: placeholderCount }).map((_, index) => (
            <div
              key={index}
              className="
              px-2
              flex-[0_0_100%]
              sm:flex-[0_0_50%]
              lg:flex-[0_0_33.333%]
            "
            >
              <div className="bg-gray-800 rounded-md animate-pulse w-full h-[130px]" />
            </div>
          ))}
        </div>

        <div className="flex justify-center gap-[5px] mt-4 md:mt-2">
          {Array.from({ length: placeholderCount }).map((_, index) => (
            <div
              key={index}
              className="size-2 rounded-full bg-bg-interactive-primary-light dark:bg-bg-interactive-primary-dark"
            />
          ))}
        </div>
      </div>
    );
  }

  if (events.length === 0) {
    return (
      <div className="flex justify-center items-center h-48 w-full bg-gray-800 rounded-md">
        <p className="text-gray-400">No featured events available.</p>
      </div>
    );
  }

  return (
    <div
      className="py-2 w-full"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex overflow-hidden" ref={emblaRef}>
        {events.map((event) => (
          <div
            key={event.id}
            className="
            px-2
            flex-[0_0_100%]
            sm:flex-[0_0_50%]
            lg:flex-[0_0_33.333%]
          "
          >
            <EventCardWithAction
              {...event}
              attendees={event.ticketsSold}
              startTime={event.startTime}
              bannerUrl={event.bannerUrl}
              title={event.title}
              id={event.id || ''}
              location={event.location}
            />
          </div>
        ))}
      </div>

      <div className="flex justify-center gap-[5px] mt-4 md:mt-2">
        {events.map((_, index) => (
          <button
            key={index}
            className={cn(
              'size-2 rounded-full transition-all duration-300',
              index === current
                ? 'w-8 bg-brand-60 scale-110'
                : 'bg-bg-interactive-primary-light dark:bg-bg-interactive-primary-dark hover:bg-gray-400 hover:scale-105'
            )}
            onClick={() => handleDotClick(index)}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};
