import { NextImage } from '@/components/ui/NextImage';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { D1, P } from '@/components/ui/typography';
import { ArrowUpRight } from 'lucide-react';

export const HeroSection = () => {
  return (
    <section className="relative min-h-[calc(100vh-144px)] lg:min-h-screen h-full max-w-[1344px] mx-auto overflow-hidden flex flex-col lg:flex-row gap-2">
      <div className="flex-1 min-h-[calc(50vh-72px)] lg:min-h-[calc(100vh-144px)] basis-0 bg-[#121212] rounded-b-[42px] lg:rounded-t-[42px] lg:pt-[208px]">
        <div className="max-w-xl pl-6 lg:pl-20 flex flex-col items-start gap-9">
          <div className="flex flex-col gap-4 md:gap-6 lg:gap-8">
            <D1 weight="bold">Here, we make our choices</D1>
            <P className="text-sm md:text-base">
              Discover events, connect with artists, and control the vibe – all
              in one app.
            </P>
            <P asChild>
              <Button
                variant="outline"
                size="lg"
                className="bg-transparent self-start gap-2 md:gap-3 border border-white hover:bg-transparent hover:text-white transition-all duration-300 rounded-full px-3 md:px-4 py-2 group text-sm md:text-base"
              >
                Download The App{' '}
                <div className="size-7 md:size-9 rounded-full flex items-center justify-center bg-white">
                  <ArrowUpRight className="text-black self-center group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform duration-300 w-4 h-4 md:w-5 md:h-5" />
                </div>
              </Button>
            </P>
          </div>

          <div className="flex gap-3 md:gap-4 items-center">
            <div className="data-[slot=avatar]:*:ring-background flex -space-x-1.5 md:-space-x-2 data-[slot=avatar]:*:ring-1 md:data-[slot=avatar]:*:ring-2 data-[slot=avatar]:*:grayscale">
              <Avatar className="w-8 h-8 md:w-10 md:h-10">
                <AvatarImage
                  src="https://github.com/shadcn.png"
                  alt="@shadcn"
                />
                <AvatarFallback>CN</AvatarFallback>
              </Avatar>
              <Avatar className="w-8 h-8 md:w-10 md:h-10">
                <AvatarImage
                  src="https://github.com/leerob.png"
                  alt="@leerob"
                />
                <AvatarFallback>LR</AvatarFallback>
              </Avatar>
              <Avatar className="w-8 h-8 md:w-10 md:h-10">
                <AvatarImage
                  src="https://github.com/evilrabbit.png"
                  alt="@evilrabbit"
                />
                <AvatarFallback>ER</AvatarFallback>
              </Avatar>
              <Avatar className="w-8 h-8 md:w-10 md:h-10">
                <AvatarImage
                  src="https://github.com/shadcn.png"
                  alt="@shadcn"
                />
                <AvatarFallback>CN</AvatarFallback>
              </Avatar>
            </div>
            <P className="text-xs md:text-sm lg:text-base">
              20k+ <br /> Popla users worldwide
            </P>
          </div>
        </div>
      </div>
      <div className="flex-1 min-h-[calc(50vh+72px)] lg:min-h-screen relative rounded-[42px] bg-[linear-gradient(135deg,#7257FF_0%,#121212_100%)] overflow-hidden">
        <NextImage
          src={
            process.env.NEXT_PUBLIC_URL
              ? `${process.env.NEXT_PUBLIC_URL}/images/popla-bg-logo.png`
              : '/images/popla-bg-logo.png'
          }
          alt="Popla background logo"
          fill
          className="object-contain"
          priority
        />

        <div className="absolute bottom-0 right-0 w-full h-full flex items-end justify-end">
          <div className="relative w-full max-w-[450px] sm:max-w-[500px] lg:max-w-[650px] xl:max-w-[720px] h-auto aspect-650/700">
            <NextImage
              src={
                process.env.NEXT_PUBLIC_URL
                  ? `${process.env.NEXT_PUBLIC_URL}/images/held-mobile-home.png`
                  : '/images/held-mobile-home.png'
              }
              alt=""
              fill
              className="object-contain object-bottom"
              priority
            />
          </div>
        </div>
      </div>

      {/* Ticket Mockup */}
      <div className="absolute z-10 left-[20%] sm:left-[35%] md:left-[42%] lg:left-[46%] xl:left-[48%] bottom-[15%] sm:bottom-[18%] md:bottom-[20%] lg:bottom-[205px] w-[80px] sm:w-[100px] md:w-[140px] lg:w-[160px] xl:w-[180px]">
        <NextImage
          src={
            process.env.NEXT_PUBLIC_URL
              ? `${process.env.NEXT_PUBLIC_URL}/images/ticket.png`
              : '/images/ticket.png'
          }
          alt="Ticket Mockup"
          width={364}
          height={104}
          className="w-full h-auto"
          priority
        />
      </div>

      {/* Event Mockup */}
      <div className="absolute z-10 left-[12%] sm:left-[28%] md:left-[35%] lg:left-[38%] xl:left-[40.6%] bottom-[5%] sm:bottom-[6%] md:bottom-[8%] lg:bottom-[68px] w-[100px] sm:w-[120px] md:w-[160px] lg:w-[180px] xl:w-[200px] rotate-3">
        <NextImage
          src={
            process.env.NEXT_PUBLIC_URL
              ? `${process.env.NEXT_PUBLIC_URL}/images/public-event.png`
              : '/images/public-event.png'
          }
          alt="Public Event Mockup"
          width={380}
          height={81}
          className="w-full h-auto"
          priority
        />
      </div>
    </section>
  );
};
