/* landing.css */
.auto-scroll {
  display: flex;
  white-space: nowrap;
  animation: scroll-left 30s linear infinite;
}

.auto-scroll-paused {
  animation-play-state: paused;
}

@keyframes scroll-left {
  0% {
    transform: translateX(50);
  }
  100% {
    transform: translateX(-100%);
  }
}

.no-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.checking {
  overflow-x: hidden;
  position: relative;
  height: 40vh;
  margin-top: 24px;
  margin-bottom: 40px;
}

.relative {
  position: relative; /* Make the container a positioning context */
}

.absolute {
  position: absolute;
}

.border-b-gray-700 {
  border-bottom-color: #4a5568; /* Triangle color */
}
