import Link from 'next/link';
import { Menu } from 'lucide-react';
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from '@/components/ui/sheet';
import { P } from '@/components/ui/typography';
import { cn } from '@/lib/utils';
import { StoreDownloadLink } from '@/components/links/store';
import { AuthButton } from '../ui/auth-button';
import { Image } from '@/components/ui';

export const Navigation = ({ className }: { className?: string }) => {
  return (
    <header className={cn('absolute w-full', className)}>
      <nav className="relative max-w-[1344px] mx-auto z-10 flex items-center justify-between px-6 py-12 lg:px-20">
        <Link href="/" className="h-full max-h-12 w-full max-w-28">
          <Image
            src={'/svgs/logo.svg'}
            alt="Popla Logo"
            width={114}
            height={48}
            className="dark:invert"
          />
        </Link>

        <div className="hidden md:flex items-center space-x-[72px]">
          <P asChild>
            <Link href="/features">Features</Link>
          </P>
          <P asChild>
            <Link href="/support">Support</Link>
          </P>
          <StoreDownloadLink />
          <P asChild>
            <Link href="/events">Events</Link>
          </P>
          <AuthButton />
        </div>

        <Sheet>
          <SheetTrigger asChild>
            <button className="md:hidden text-white hover:bg-white/10">
              <Menu className="h-6 w-6" />
              <span className="sr-only">Toggle menu</span>
            </button>
          </SheetTrigger>
          <SheetContent
            side="right"
            className="bg-black/95 border-purple-500/20"
          >
            <div className="flex flex-col space-y-6 mt-8">
              <Link
                href="/features"
                className="text-white/90 hover:text-white transition-colors duration-200 font-medium text-lg"
              >
                Features
              </Link>
              <Link
                href="/support"
                className="text-white/90 hover:text-white transition-colors duration-200 font-medium text-lg"
              >
                Support
              </Link>
              <Link
                href="/download"
                className="text-white/90 hover:text-white transition-colors duration-200 font-medium text-lg"
              >
                Download
              </Link>
              <Link
                href="/events"
                className="text-white/90 hover:text-white transition-colors duration-200 font-medium text-lg"
              >
                Events
              </Link>
              <AuthButton />
            </div>
          </SheetContent>
        </Sheet>
      </nav>
    </header>
  );
};
