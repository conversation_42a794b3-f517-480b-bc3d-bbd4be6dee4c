'use client';

import { HelpCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { D1, H1, H4, Large } from '@/components/ui';

const faqData = [
  {
    id: 'item-1',
    question: 'Donec ac odio tempor orci dapibus ultrices?',
    answer: '',
  },
  {
    id: 'item-2',
    question: 'Donec ac odio tempor orci dapibus ultrices?',
    answer: '',
  },
  {
    id: 'item-3',
    question: 'Ut lectus arcu bibendum at varius vel pharetra vel?',
    answer: '',
  },
  {
    id: 'item-4',
    question: 'Ut lectus arcu bibendum at varius vel pharetra vel?',
    answer: '',
  },
  {
    id: 'item-5',
    question: 'Enim sed faucibus turpis in eu mi bibendum?',
    answer:
      'Bibendum at varius vel pharetra vel. Con<PERSON><PERSON> aenean et tortor at risus viverra adipiscing. Est pellentesque elit ullamcorper dignissim cras. In pellentesque massa placerat duis ultricies.',
  },
  {
    id: 'item-6',
    question: 'Enim sed faucibus turpis in eu mi bibendum?',
    answer:
      'Mattis ullamcorper velit sed ullamcorper morbi tincidunt. In metus vulputate eu scelerisque felis imperdiet proin fermentum leo. Diam quis enim lobortis scelerisque fermentum dui faucibus.',
  },
  {
    id: 'item-7',
    question: 'Mi bibendum neque egestas congue quisque?',
    answer: '',
  },
  {
    id: 'item-8',
    question: 'Mi bibendum neque egestas congue quisque?',
    answer: '',
  },
  {
    id: 'item-9',
    question: 'Egestas diam in arcu. Elit pellentesque habitant?',
    answer: '',
  },
  {
    id: 'item-10',
    question: 'Egestas diam in arcu. Elit pellentesque habitant?',
    answer: '',
  },
];

export const SupportSection = () => {
  return (
    <section className="pt-[176px] flex flex-col gap-4 mb-10">
      <div className="relative min-h-[416px] h-full flex flex-col gap-12 justify-center items-center rounded-[32px] bg-[#0A0A0A]">
        <div className="flex flex-col gap-6">
          <D1 weight="bold">Still Have Any Questions?</D1>
          <Large className="text-center">
            Then don&apos;t hesitate to get in touch with us. We&apos;ll help in
            any way we can.
          </Large>
          <div className="self-center">
            <Button variant="outline" label="Contact Us" />
          </div>
        </div>
      </div>

      <div className="min-h-[731px] h-full flex flex-col">
        <div className="flex flex-col items-center gap-16 m-auto">
          <div className="flex flex-col gap-6 justify-center items-center">
            <H1 weight="bold">Frequently Asked Questions</H1>
            <Large weight="regular">
              Fermentum iaculis eu non diam phasellus vestibulum. Volutpat
              commodo sed egestas egestas fringilla phasellus. Donec ac odio
              tempor orci dapibus ultrices.
            </Large>
          </div>

          <div className="grid md:grid-cols-2 gap-x-8 gap-y-4 w-full">
            {[
              faqData.slice(0, Math.ceil(faqData.length / 2)),
              faqData.slice(Math.ceil(faqData.length / 2)),
            ].map((faqColumn, colIndex) => (
              <Accordion key={colIndex} type="multiple" className="w-full">
                {faqColumn.map((faq) => (
                  <AccordionItem
                    key={faq.id}
                    value={faq.id}
                    className="border-b border-[#CBD5E1] w-full"
                  >
                    <AccordionTrigger className="hover:no-underline hover:bg-transparent p-4 [&[data-state=open]>svg]:rotate-180">
                      <div className="flex items-center gap-2 text-left">
                        <HelpCircle className="size-6 text-white shrink-0" />
                        <H4 as="span" weight="bold">
                          {faq.question}
                        </H4>
                      </div>
                    </AccordionTrigger>
                    {faq.answer && (
                      <AccordionContent className="pb-6 pl-8">
                        <p className="text-gray-400 leading-relaxed">
                          {faq.answer}
                        </p>
                      </AccordionContent>
                    )}
                  </AccordionItem>
                ))}
              </Accordion>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};
