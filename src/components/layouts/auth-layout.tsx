import React from 'react';

import { H1, P } from '@/components/ui';
import { Back } from '../ui/back';
import { DotStepIndicator, type DotStepIndicatorProps } from '../ui';

type AuthLayoutProps = {
  title: string;
  subTitle?: string;
  onBackPress?: () => void;
  children: React.ReactNode;
  footer?: React.ReactNode;
  showBackButton?: boolean;
  pagination?: DotStepIndicatorProps;
};

export const AuthLayout = ({
  title,
  subTitle,
  showBackButton = true,
  onBackPress,
  children,
  footer,
  pagination,
}: AuthLayoutProps) => {
  return (
    <div className="flex flex-col flex-1 bg-bg-canvas-light dark:bg-bg-canvas-dark px-4">
      <div className="flex flex-row items-center justify-between py-3">
        {showBackButton && <Back onBackPress={onBackPress} />}
        {pagination && (
          <DotStepIndicator
            activeStep={pagination.activeStep}
            totalSteps={pagination.totalSteps}
          />
        )}
        <div className="w-8" />
      </div>

      <div className="mb-4">
        <H1>{title}</H1>
        {subTitle && (
          <P className="mb-4 mt-1 text-grey-60 dark:text-grey-50">{subTitle}</P>
        )}
      </div>
      <div className="mx-auto flex w-full max-w-md flex-1 flex-col">
        {children}
        {footer && <div className="mt-auto">{footer}</div>}
      </div>
    </div>
  );
};
