'use client';
import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { FloatingInput } from '@/components/ui/floating-input';
import { PasswordInput } from '@/components/ui/password-input';
import { H1, MdBoldLabel } from '@/components/ui/typography';
import { cn, PASSWORD_REGEX } from '@/lib/utils';
import { UnstyledLink } from '@/components/links/unstyled';
import { getQueryClient } from '@/api';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { useAuth } from '@/lib/hooks/use-auth';
import toast from 'react-hot-toast';
import { useRouter } from 'next/navigation';

const loginSchema = z.object({
  email: z.email('Invalid email format').nonempty('Email is required'),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters long')
    .regex(
      PASSWORD_REGEX.twoLowerCaseRegex,
      'Must contain at least 2 lowercase letters'
    )
    .regex(
      PASSWORD_REGEX.oneUpperCaseRegex,
      'Must contain at least 1 uppercase letter'
    )
    .regex(PASSWORD_REGEX.twoDigitsRegex, 'Must contain at least 1 digit')
    .regex(
      PASSWORD_REGEX.oneSpecialCharacter,
      'Must contain at least 1 special character'
    ),
  rememberMe: z.boolean().optional(),
});

export type LoginFormValues = z.infer<typeof loginSchema>;

interface LoginFormProps
  extends Omit<React.ComponentPropsWithoutRef<'form'>, 'onSubmit'> {
  onSubmit?: (values: LoginFormValues) => Promise<void> | void;
}
export const LoginForm = ({
  className,
  onSubmit,
  ...props
}: LoginFormProps) => {
  const { login, isLoggingIn } = useAuth();
  const router = useRouter();
  const queryClient = getQueryClient();
  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  });

  const handleSubmit = async (data: LoginFormValues) => {
    const { email, password } = data;
    try {
      login(
        { email, password },
        {
          onSuccess: () => {
            toast.success('Logged in');
            router.push('/events');
            queryClient.invalidateQueries({ queryKey: ['getUser'] });
          },
          onError: (error) => {
            toast.error(error.message);
          },
        }
      );
      await onSubmit?.(data);
      console.log('Login data:', data);
    } catch (error) {
      console.error('Login error:', error);
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className={cn('flex flex-col gap-8', className)}
        {...props}
      >
        <H1 themed weight="bold">
          Get back to your vibe!
        </H1>

        <div className="grid gap-6">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <FloatingInput
                    type="email"
                    label="Email"
                    required
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <PasswordInput
                    label="Password"
                    value={field.value}
                    onChange={field.onChange}
                    autoComplete="current-password"
                    required
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <MdBoldLabel
            className="flex-1 text-accent-moderate"
            weight="bold"
            asChild
          >
            <UnstyledLink href="/forgot-password">
              Forgot password?
            </UnstyledLink>
          </MdBoldLabel>

          {/* <FormField
            control={form.control}
            name="rememberMe"
            render={({ field }) => (
              <FormItem className="flex gap-4 items-center">
                <P className="flex-1 text-fg-muted-light dark:text-fg-muted-dark">
                  Remember sign in details
                </P>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          /> */}
        </div>

        <Button
          className="h-12 bg-accent-moderate rounded-full font-bold text-base/none py-4 text-white"
          type="submit"
          label="Log in"
          disabled={isLoggingIn}
          loading={isLoggingIn}
        />

        <div className="text-center text-fg-subtle-light dark:text-fg-subtle-dark text-sm mt-2">
          Don&apos;t have an account?{' '}
          <UnstyledLink href="/register" className="text-accent-moderate">
            Sign up
          </UnstyledLink>
        </div>
      </form>
    </Form>
  );
};
