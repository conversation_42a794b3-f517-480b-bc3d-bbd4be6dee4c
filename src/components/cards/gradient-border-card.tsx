'use client';

import React from 'react';
import { MdBoldLabel, Small } from '@/components/ui';
import { cn } from '@/lib/utils';
import Image from 'next/image';

interface GradientBorderCardProps {
  title: string;
  description?: string;
  icon: string;
  isSelected: boolean;
  onClick?: () => void;
}

export const GradientBorderCard = ({
  title,
  description,
  onClick,
  icon,
  isSelected,
}: GradientBorderCardProps) => {
  return (
    <button
      onClick={onClick}
      className={cn(
        'relative w-full h-[92px] rounded-xl p-[3px] cursor-pointer',
        isSelected
          ? 'animate-background bg-[radial-gradient(circle,#5336E2_0%,transparent_100%),radial-gradient(circle,#E733E0_0%,transparent_100%),radial-gradient(circle,#FFFFFF_0%,transparent_100%)] bg-[length:_400%_400%] [animation-duration:_4s]'
          : 'bg-transparent'
      )}
    >
      <div className="w-full h-full rounded-xl flex items-center justify-center bg-bg-canvas-light dark:bg-bg-canvas-dark px-4 py-3">
        <CardContent icon={icon} title={title} description={description} />
      </div>
    </button>
  );
};

interface CardContentProps {
  title: string;
  description?: string;
  icon: string;
}

const CardContent = ({ icon, title, description }: CardContentProps) => (
  <div className="relative z-10 flex w-full flex-row gap-4 items-center">
    <Image
      className="my-auto rounded object-cover"
      src={icon}
      alt={title}
      width={40}
      height={40}
    />
    {description ? (
      <div className="w-full max-w-[273px] flex flex-col gap-2 items-start">
        <MdBoldLabel>{title}</MdBoldLabel>
        <Small className="text-gray-600 dark:text-gray-400 text-start">
          {description}
        </Small>
      </div>
    ) : (
      <MdBoldLabel className="my-auto">{title}</MdBoldLabel>
    )}
  </div>
);
