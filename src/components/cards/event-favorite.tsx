'use client';
import { useState } from 'react';
import Link from 'next/link';
import { FaRegCalendarAlt } from 'react-icons/fa';
import { IoGlobeOutline, IoLocationOutline } from 'react-icons/io5';
import { GoHeart, GoHeartFill } from 'react-icons/go';
import dayjs from 'dayjs';

import { EventFormat, type IEvent } from '@/api/events';
import { useFavoriteToggle } from '@/lib';
import { ConfirmationDialog } from '../dialogs';
import { colors, Image, MdBoldLabel, Tiny } from '@/components/ui';

interface EventFavoriteCardProps extends IEvent {
  attendees: number;
  isFavoriteTab?: boolean;
}

export const EventFavoriteCard: React.FC<EventFavoriteCardProps> = ({
  title,
  bannerUrl,
  startTime,
  location,
  id,
  slug,
  eventFormat,
  onlineEventUrl,
  isfavourite,
  isFavoriteTab,
  status,
}) => {
  const { favourited, handleFavToggle } = useFavoriteToggle({
    initialFavoriteState: isfavourite || false,
    payload: { type: 'EVENT', accountId: null, eventId: id },
  });

  const [confirmVisible, setConfirmVisible] = useState(false);

  const hasPhysicalLocation = eventFormat !== EventFormat.ONLINE;
  const eventAddress = hasPhysicalLocation
    ? location.landmark ||
      location.address.replace(/^CCQH\+9HP\s*,?\s*/, '').trim()
    : onlineEventUrl || 'Online';

  return (
    <Link href={`/events/${slug}`} className="block">
      <div className="h-[152px] flex gap-4 rounded-lg bg-bg-subtle-light p-4 dark:bg-bg-subtle-dark">
        <Image
          src={bannerUrl}
          alt={title}
          width={120}
          height={120}
          className="w-[120px] h-[120px] rounded-lg object-cover"
        />

        <div className="flex flex-1 flex-col gap-2">
          <div className="flex flex-row items-center justify-between gap-2">
            <MdBoldLabel className="flex-1 truncate">{title}</MdBoldLabel>
            <button
              onClick={(e) => {
                e.preventDefault();
                if (isFavoriteTab) {
                  setConfirmVisible(true);
                } else {
                  handleFavToggle();
                }
              }}
              className="shrink-0"
            >
              {favourited ? (
                <GoHeartFill
                  size={24}
                  color={favourited ? colors.red[50] : colors.brand[40]}
                />
              ) : (
                <GoHeart
                  size={24}
                  color={favourited ? colors.red[50] : colors.brand[40]}
                />
              )}
            </button>
          </div>

          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-1 self-start border border-brand-30 rounded-full bg-white px-2 py-1 dark:border-brand-80 dark:bg-grey-100">
              <FaRegCalendarAlt size={16} color={colors.brand[40]} />
              <Tiny className="shrink text-brand-70 dark:text-brand-40">
                {dayjs(startTime).format('D MMM')}
              </Tiny>
            </div>

            <div className="flex items-center gap-1 self-start border border-brand-30 rounded-full bg-white px-2 py-1 dark:border-brand-80 dark:bg-grey-100">
              {eventFormat === EventFormat.ONLINE ? (
                <IoGlobeOutline size={16} color={colors.brand[40]} />
              ) : (
                <IoLocationOutline size={16} color={colors.brand[40]} />
              )}
              <Tiny className="shrink text-brand-70 dark:text-brand-40">
                {eventAddress}
              </Tiny>
            </div>

            {status === 'DRAFT' && (
              <div className="flex justify-center self-start rounded-full bg-bg-warning-light px-2 py-1 dark:bg-bg-warning-dark">
                <Tiny className="text-dark dark:text-white">Pending</Tiny>
              </div>
            )}
          </div>
        </div>

        <ConfirmationDialog
          visible={confirmVisible}
          message="Are you sure you want to remove this event from your favourites?"
          onCancel={() => setConfirmVisible(false)}
          onConfirm={() => {
            setConfirmVisible(false);
            handleFavToggle();
          }}
        />
      </div>
    </Link>
  );
};
