import { Calendar, MapPin } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import moment from 'moment';
import React from 'react';

import { type IEvent } from '@/api/events';
import { cn, useFavoriteToggle } from '@/lib';
import { colors, H4 } from '@/components/ui';
import { IoHeartOutline, IoHeart } from 'react-icons/io5';

interface PortraitEventCardProps extends IEvent {
  isEven?: boolean;
  hasPhysicalLocation: boolean;
}

export const PortraitEventCard: React.FC<PortraitEventCardProps> = ({
  bannerUrl,
  title,
  startTime,
  location,
  isEven = false,
  id,
  slug,
  isfavourite,
  hasPhysicalLocation,
  onlineEventUrl,
}) => {
  const { favourited, handleFavToggle } = useFavoriteToggle({
    initialFavoriteState: isfavourite,
    payload: { type: 'EVENT', accountId: null, eventId: id },
  });

  const locationText = hasPhysicalLocation
    ? location.landmark ||
      location.address.replace(/^CCQH\+9HP\s*,?\s*/, '').trim()
    : onlineEventUrl || 'Online';

  return (
    <Link
      href={`/events/${slug}`}
      className={cn(
        'relative w-full flex-1 rounded-lg aspect-[4/4]',
        isEven ? 'mr-2' : ''
      )}
    >
      <Image
        src={bannerUrl || '/images/gradient-bg/event-gradient.png'}
        alt={title}
        fill
        className="object-cover rounded-lg"
      />

      <button
        type="button"
        onClick={(e) => {
          e.preventDefault();
          handleFavToggle();
        }}
        className="absolute top-2 right-2 z-10 size-8 flex items-center justify-center rounded-full bg-brand-20 dark:bg-brand-90"
      >
        {favourited ? (
          <IoHeart size={16} color={colors.red[50]} />
        ) : (
          <IoHeartOutline size={16} color={colors.brand[40]} />
        )}
      </button>

      <div className="absolute left-2 bottom-2 right-2 p-4 pt-6 rounded-lg backdrop-blur-md bg-white/5">
        <H4 className="text-white font-bold">{title}</H4>
        <div className="flex flex-col gap-2 mt-2">
          <div className="flex items-center gap-1 text-white text-sm">
            <Calendar size={16} />
            <span>{moment.utc(startTime).format('D MMM YYYY [at] HH:mm')}</span>
          </div>
          <div className="flex items-center gap-1 text-white text-sm">
            <MapPin size={16} />
            <span className="truncate">{locationText}</span>
          </div>
        </div>
      </div>
    </Link>
  );
};
