import * as React from 'react';

import { type IEvent } from '@/api/events';
import { EventFavoriteCard } from '../cards/event-favorite';
import { EmptyState } from '../ui/empty';
import { LoadingScreen } from '@/components/loaders';

type Props = {
  events?: IEvent[];
  index: number;
  isPending: boolean;
};

export const CreatorEventsTab = ({ events, isPending }: Props) => {
  return (
    <div className="flex-1 bg-white dark:bg-grey-100 min-h-screen overflow-y-auto">
      <div className="flex-1 p-4">
        {isPending ? (
          <LoadingScreen />
        ) : events && events.length > 0 ? (
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {events.map((event) => (
              <EventFavoriteCard
                key={event.id}
                {...event}
                attendees={event.ticketsSold}
              />
            ))}
          </div>
        ) : (
          <EmptyState />
        )}
      </div>
    </div>
  );
};
